# Sparkle-AI 项目规划文档

## 项目概述

### 项目名称
Sparkle-AI - 智能系统助手

### 项目目标
构建一个基于AI的系统助手，通过AppImage格式部署在Ubuntu 24.04系统上，为用户提供便捷的AI交互和系统操作能力。

### 核心价值
- 快捷AI交互：通过全局快捷键快速调用AI能力
- 智能记忆管理：自动记录和整理用户交互历史
- 系统深度集成：通过工具调用实现与系统的无缝交互

## 技术架构方案

### 技术栈选择

#### 前端框架
- **主框架**: Tauri (Rust + Web前端)
  - 优势：轻量级、安全性高、跨平台、易于打包AppImage
  - 前端技术：React + TypeScript + Tailwind CSS
  - 状态管理：Zustand

#### 后端技术
- **核心语言**: Rust
  - 高性能、内存安全、并发处理能力强
- **数据库**: SQLite
  - 轻量级、无需额外部署、适合桌面应用
- **HTTP客户端**: reqwest
- **异步运行时**: tokio

#### AI集成
- **模型接入**:
  - DeepSeek API
  - 通义千问 API  
  - Ollama (本地模型)
- **工具调用框架**: 自定义工具系统

#### 系统集成
- **全局快捷键**: global-hotkey crate
- **系统托盘**: tray-icon crate
- **Shell执行**: std::process::Command

### 项目结构

```
sparkle-ai/
├── src-tauri/           # Rust后端代码
│   ├── src/
│   │   ├── main.rs
│   │   ├── ai/          # AI模块
│   │   ├── tools/       # 工具调用模块
│   │   ├── memory/      # 记忆管理模块
│   │   ├── config/      # 配置管理
│   │   └── database/    # 数据库操作
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                 # 前端代码
│   ├── components/      # React组件
│   ├── pages/          # 页面组件
│   ├── hooks/          # 自定义hooks
│   ├── stores/         # 状态管理
│   └── utils/          # 工具函数
├── public/
├── package.json
└── README.md
```

## 功能需求详细设计

### 1. 快捷操作模块

#### 功能描述
- 全局快捷键唤起（默认Ctrl+Q）
- 浮动输入框界面
- 快速AI查询和响应

#### 技术实现
- 使用global-hotkey监听系统快捷键
- 创建无边框浮动窗口
- 实现快速AI调用接口

#### 用户交互流程
1. 用户按下Ctrl+Q
2. 弹出输入框（居中显示）
3. 用户输入问题
4. AI处理并返回结果
5. 显示结果或执行相应操作

### 2. 主界面模块

#### 2.1 AI对话功能

**核心特性**:
- 实时对话界面
- 历史记录查看
- 工具调用支持
- 多模型切换

**工具系统设计**:
```rust
pub trait Tool {
    fn name(&self) -> &str;
    fn description(&self) -> &str;
    fn execute(&self, params: serde_json::Value) -> Result<String, ToolError>;
}
```

**已规划工具**:
- `safe_shell`: 安全Shell命令执行
- `gaode_map`: 高德地图API集成（天气、地点查询）

#### 2.2 记忆管理系统

**记忆分类**:
- 重要记忆：用户明确标记或AI判断的关键信息
- 一般记忆：日常对话中的上下文信息

**记忆生命周期**:
1. **生成阶段**: 每次对话后AI自动生成记忆
2. **检索阶段**: 对话前自动搜索相关记忆
3. **整理阶段**: 每20条记忆触发AI整理
   - 相似记忆合并
   - 过期记忆清理
   - 重要性重新评估

**数据库设计**:
```sql
CREATE TABLE memories (
    id INTEGER PRIMARY KEY,
    content TEXT NOT NULL,
    importance_level INTEGER, -- 1:一般 2:重要
    created_at TIMESTAMP,
    last_accessed TIMESTAMP,
    embedding BLOB, -- 向量嵌入用于相似性搜索
    tags TEXT -- JSON格式标签
);
```

#### 2.3 设置模块

**模型配置**:
- DeepSeek API配置
- 通义千问API配置  
- Ollama本地模型配置

**参数设置**:
- 温度参数
- 最大token数
- 对话窗口长度（保留多少轮对话）

**界面配置**:
- 主题设置
- 字体大小
- 快捷键自定义

## 开发计划

### 第一阶段：基础框架搭建（2周）
- [ ] Tauri项目初始化
- [ ] 基础UI框架搭建
- [ ] 数据库设计和初始化
- [ ] 配置管理系统

### 第二阶段：核心功能开发（3周）
- [ ] AI对话功能实现
- [ ] 快捷键系统开发
- [ ] 基础工具系统（safe_shell）
- [ ] 记忆管理基础功能

### 第三阶段：高级功能（2周）
- [ ] 记忆智能整理
- [ ] 高德地图工具集成
- [ ] 设置界面完善
- [ ] 性能优化

### 第四阶段：测试和打包（1周）
- [ ] 功能测试
- [ ] AppImage打包配置
- [ ] 部署测试
- [ ] 文档完善

## 技术风险和解决方案

### 风险1：全局快捷键冲突
**解决方案**: 提供快捷键自定义功能，检测冲突并提示用户

### 风险2：AI API限制和成本
**解决方案**: 
- 实现多模型切换
- 支持本地Ollama模型
- 添加使用量监控

### 风险3：记忆系统性能
**解决方案**:
- 使用向量数据库进行相似性搜索
- 实现记忆分页和懒加载
- 定期清理过期数据

### 风险4：AppImage兼容性
**解决方案**:
- 在多个Ubuntu版本测试
- 静态链接关键依赖
- 提供详细的系统要求文档

## 成功指标

### 功能指标
- [ ] 快捷键响应时间 < 500ms
- [ ] AI对话响应时间 < 3s
- [ ] 记忆检索准确率 > 85%
- [ ] 系统资源占用 < 200MB

### 用户体验指标
- [ ] 界面响应流畅度
- [ ] 工具调用成功率 > 95%
- [ ] 用户满意度调研

## 后续扩展计划

### 短期扩展（3个月内）
- 更多工具集成（文件操作、网络查询等）
- 语音交互支持
- 多语言支持

### 长期扩展（6个月内）
- 插件系统
- 云端记忆同步
- 团队协作功能