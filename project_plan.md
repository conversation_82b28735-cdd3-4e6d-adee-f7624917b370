# Sparkle-AI 项目规划文档

## 项目概述

### 项目名称
Sparkle-AI - 智能系统助手

### 项目目标
构建一个基于AI的系统助手，通过AppImage格式部署在Ubuntu 24.04系统上，为用户提供便捷的AI交互和系统操作能力。

### 核心价值
- 快捷AI交互：通过全局快捷键快速调用AI能力
- 智能记忆管理：自动记录和整理用户交互历史
- 系统深度集成：通过工具调用实现与系统的无缝交互

## 技术架构方案

### 技术栈选择

#### 前端框架
- **主框架**: Tauri (Rust + Web前端)
  - 优势：轻量级、安全性高、跨平台、易于打包AppImage
  - 前端技术：React + TypeScript + Tailwind CSS
  - 状态管理：Zustand

#### 后端技术
- **核心语言**: Rust
  - 高性能、内存安全、并发处理能力强
- **数据库**: SQLite
  - 轻量级、无需额外部署、适合桌面应用
- **HTTP客户端**: reqwest
- **异步运行时**: tokio

#### AI集成
- **模型接入**:
  - DeepSeek API
  - 通义千问 API  
  - Ollama (本地模型)
- **工具调用框架**: 自定义工具系统

#### 系统集成
- **全局快捷键**: global-hotkey crate
- **系统托盘**: tray-icon crate
- **Shell执行**: std::process::Command

### 项目结构

```
sparkle-ai/
├── src-tauri/           # Rust后端代码
│   ├── src/
│   │   ├── main.rs
│   │   ├── ai/          # AI模块
│   │   │   ├── mod.rs
│   │   │   ├── models/  # 不同AI模型实现
│   │   │   ├── manager.rs
│   │   │   └── types.rs
│   │   ├── tools/       # 工具调用模块
│   │   │   ├── mod.rs
│   │   │   ├── shell.rs
│   │   │   ├── file_manager.rs
│   │   │   ├── system_info.rs
│   │   │   └── web_search.rs
│   │   ├── memory/      # 记忆管理模块
│   │   │   ├── mod.rs
│   │   │   ├── storage.rs
│   │   │   ├── embedding.rs
│   │   │   └── retrieval.rs
│   │   ├── config/      # 配置管理
│   │   │   ├── mod.rs
│   │   │   └── settings.rs
│   │   ├── database/    # 数据库操作
│   │   │   ├── mod.rs
│   │   │   ├── models.rs
│   │   │   └── migrations.rs
│   │   ├── security/    # 安全模块
│   │   │   ├── mod.rs
│   │   │   └── validator.rs
│   │   └── api/         # API接口
│   │       ├── mod.rs
│   │       ├── chat.rs
│   │       ├── memory.rs
│   │       └── tools.rs
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                 # 前端代码
│   ├── components/      # React组件
│   │   ├── Chat/
│   │   ├── QuickInput/
│   │   ├── Settings/
│   │   └── Common/
│   ├── pages/          # 页面组件
│   │   ├── MainPage.tsx
│   │   ├── SettingsPage.tsx
│   │   └── MemoryPage.tsx
│   ├── hooks/          # 自定义hooks
│   │   ├── useChat.ts
│   │   ├── useMemory.ts
│   │   └── useSettings.ts
│   ├── stores/         # 状态管理
│   │   ├── chatStore.ts
│   │   ├── memoryStore.ts
│   │   └── settingsStore.ts
│   ├── utils/          # 工具函数
│   │   ├── api.ts
│   │   ├── constants.ts
│   │   └── helpers.ts
│   └── types/          # TypeScript类型定义
│       ├── chat.ts
│       ├── memory.ts
│       └── api.ts
├── public/
├── tests/              # 测试文件
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── docs/               # 文档
│   ├── api.md
│   ├── deployment.md
│   └── user-guide.md
├── package.json
├── README.md
└── CHANGELOG.md
```

### API接口设计

#### 前后端通信接口（Tauri Commands）

```rust
// 聊天相关API
#[tauri::command]
async fn send_message(message: String, model: Option<String>) -> Result<ChatResponse, String>;

#[tauri::command]
async fn get_chat_history(limit: Option<usize>) -> Result<Vec<ChatMessage>, String>;

#[tauri::command]
async fn clear_chat_history() -> Result<(), String>;

// 记忆管理API
#[tauri::command]
async fn search_memories(query: String, limit: Option<usize>) -> Result<Vec<Memory>, String>;

#[tauri::command]
async fn add_memory(content: String, importance: u8) -> Result<Memory, String>;

#[tauri::command]
async fn update_memory(id: i64, content: String, importance: u8) -> Result<(), String>;

#[tauri::command]
async fn delete_memory(id: i64) -> Result<(), String>;

// 工具调用API
#[tauri::command]
async fn execute_tool(tool_name: String, params: serde_json::Value) -> Result<ToolResult, String>;

#[tauri::command]
async fn get_available_tools() -> Result<Vec<ToolInfo>, String>;

// 配置管理API
#[tauri::command]
async fn get_settings() -> Result<AppSettings, String>;

#[tauri::command]
async fn update_settings(settings: AppSettings) -> Result<(), String>;

#[tauri::command]
async fn reset_settings() -> Result<(), String>;

// 系统API
#[tauri::command]
async fn get_system_info() -> Result<SystemInfo, String>;

#[tauri::command]
async fn check_updates() -> Result<UpdateInfo, String>;
```

#### 数据类型定义

```rust
#[derive(Serialize, Deserialize)]
pub struct ChatMessage {
    pub id: i64,
    pub role: String,
    pub content: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub model_used: Option<String>,
    pub tool_calls: Option<Vec<ToolCall>>,
}

#[derive(Serialize, Deserialize)]
pub struct ChatResponse {
    pub message: String,
    pub tool_results: Option<Vec<ToolResult>>,
    pub memories_used: Vec<Memory>,
    pub tokens_used: Option<u32>,
    pub cost: Option<f64>,
}

#[derive(Serialize, Deserialize)]
pub struct Memory {
    pub id: i64,
    pub content: String,
    pub summary: Option<String>,
    pub importance_level: u8,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_accessed: chrono::DateTime<chrono::Utc>,
    pub access_count: u32,
    pub tags: Vec<String>,
}

#[derive(Serialize, Deserialize)]
pub struct ToolResult {
    pub tool_name: String,
    pub success: bool,
    pub result: String,
    pub execution_time: u64,
    pub error: Option<String>,
}

#[derive(Serialize, Deserialize)]
pub struct AppSettings {
    pub ai_settings: AISettings,
    pub ui_settings: UISettings,
    pub security_settings: SecuritySettings,
    pub performance_settings: PerformanceSettings,
}
```

## 功能需求详细设计

### 1. 快捷操作模块

#### 功能描述
- 全局快捷键唤起（默认Ctrl+Q，可自定义）
- 浮动输入框界面（类似Spotlight/Alfred）
- 快速AI查询和响应
- 智能命令识别和执行
- 历史命令快速访问

#### 技术实现
- 使用global-hotkey监听系统快捷键
- 创建无边框浮动窗口（Always on top）
- 实现快速AI调用接口
- 命令缓存和预测机制
- 键盘导航支持

#### 用户交互流程
1. 用户按下Ctrl+Q
2. 弹出输入框（屏幕中央，半透明背景）
3. 用户输入问题或命令
4. 实时显示匹配的历史命令/建议
5. AI处理并返回结果
6. 显示结果或执行相应操作
7. 按ESC或点击外部区域关闭

#### 界面设计细节
- 输入框：圆角设计，阴影效果
- 结果显示：可滚动区域，支持富文本
- 快捷操作按钮：复制、执行、保存等
- 状态指示器：加载动画、错误提示
- 键盘快捷键：Tab补全、上下箭头选择历史

### 2. 主界面模块

#### 2.1 AI对话功能

**核心特性**:
- 实时对话界面
- 历史记录查看
- 工具调用支持
- 多模型切换

**工具系统设计**:
```rust
pub trait Tool {
    fn name(&self) -> &str;
    fn description(&self) -> &str;
    fn execute(&self, params: serde_json::Value) -> Result<String, ToolError>;
}
```

**已规划工具**:
- `safe_shell`: 安全Shell命令执行
  - 白名单命令机制
  - 参数验证和转义
  - 执行超时控制
  - 输出长度限制
- `gaode_map`: 高德地图API集成（天气、地点查询）
  - 实时天气查询
  - 地点搜索和导航
  - 周边服务查询
- `file_manager`: 文件系统操作
  - 安全的文件读写
  - 目录浏览和搜索
  - 文件类型识别
  - 权限检查机制
- `system_info`: 系统信息查询
  - CPU、内存、磁盘使用率
  - 进程管理
  - 网络状态监控
  - 系统日志查看
- `web_search`: 网络搜索工具
  - 搜索引擎API集成
  - 结果摘要和过滤
  - 网页内容提取
- `calendar_manager`: 日程管理
  - 事件创建和提醒
  - 日历同步
  - 时间冲突检测
- `note_taking`: 笔记管理
  - 快速记录
  - 标签分类
  - 全文搜索
  - Markdown支持

#### 2.2 记忆管理系统

**记忆分类**:
- 重要记忆：用户明确标记或AI判断的关键信息
- 一般记忆：日常对话中的上下文信息

**记忆生命周期**:
1. **生成阶段**: 每次对话后AI自动生成记忆
   - 使用专门的记忆提取prompt
   - 自动识别关键信息和上下文
   - 生成结构化记忆条目
2. **检索阶段**: 对话前自动搜索相关记忆
   - 基于语义相似度的向量搜索
   - 时间衰减权重计算
   - 上下文相关性评分
   - 最多检索5条最相关记忆
3. **整理阶段**: 每20条记忆触发AI整理
   - 相似记忆合并（相似度>0.85）
   - 过期记忆清理（30天未访问且重要性<2）
   - 重要性重新评估
   - 记忆关系图构建

**向量嵌入实现**:
- 使用sentence-transformers模型
- 本地部署轻量级嵌入模型（如all-MiniLM-L6-v2）
- 384维向量存储
- 余弦相似度计算

**记忆检索算法**:
```rust
pub struct MemoryRetrieval {
    similarity_threshold: f32,  // 0.7
    time_decay_factor: f32,     // 0.1
    max_results: usize,         // 5
}

impl MemoryRetrieval {
    fn calculate_score(&self, memory: &Memory, query_embedding: &[f32]) -> f32 {
        let similarity = cosine_similarity(&memory.embedding, query_embedding);
        let time_factor = self.calculate_time_decay(memory.last_accessed);
        let importance_factor = memory.importance_level as f32 / 2.0;

        similarity * 0.6 + time_factor * 0.2 + importance_factor * 0.2
    }
}
```

**数据库设计**:
```sql
-- 记忆表
CREATE TABLE memories (
    id INTEGER PRIMARY KEY,
    content TEXT NOT NULL,
    summary TEXT,               -- AI生成的摘要
    importance_level INTEGER,   -- 1:一般 2:重要 3:关键
    created_at TIMESTAMP,
    last_accessed TIMESTAMP,
    access_count INTEGER DEFAULT 0,
    embedding BLOB,            -- 向量嵌入用于相似性搜索
    tags TEXT,                 -- JSON格式标签
    source_type TEXT,          -- 来源类型：chat, tool, user_input
    related_memories TEXT      -- JSON格式的相关记忆ID列表
);

-- 对话历史表
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY,
    session_id TEXT NOT NULL,
    role TEXT NOT NULL,        -- user, assistant, system
    content TEXT NOT NULL,
    tool_calls TEXT,           -- JSON格式的工具调用记录
    timestamp TIMESTAMP,
    model_used TEXT
);

-- 工具使用记录表
CREATE TABLE tool_usage (
    id INTEGER PRIMARY KEY,
    tool_name TEXT NOT NULL,
    parameters TEXT,           -- JSON格式参数
    result TEXT,
    success BOOLEAN,
    execution_time INTEGER,    -- 毫秒
    timestamp TIMESTAMP,
    conversation_id INTEGER,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id)
);

-- 用户配置表
CREATE TABLE user_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP
);
```

#### 2.3 设置模块

**模型配置**:
- DeepSeek API配置
  - API密钥管理
  - 请求频率限制
  - 模型版本选择
- 通义千问API配置
  - 阿里云账户集成
  - 区域选择
  - 计费监控
- Ollama本地模型配置
  - 模型下载和管理
  - GPU加速设置
  - 内存使用限制

**AI参数设置**:
- 温度参数 (0.1-2.0)
- 最大token数 (100-4000)
- 对话窗口长度（保留多少轮对话，默认10轮）
- 系统提示词自定义
- 工具调用权限控制

**界面配置**:
- 主题设置（亮色/暗色/自动）
- 字体大小 (12-24px)
- 快捷键自定义
- 窗口透明度
- 动画效果开关
- 语言设置

**安全设置**:
- 工具执行权限管理
- 敏感命令黑名单
- 数据加密选项
- 自动备份设置

**性能设置**:
- 记忆检索数量限制
- 向量计算并发数
- 缓存大小设置
- 日志级别配置

## 开发计划

### 第一阶段：基础框架搭建（2周）
**Week 1:**
- [ ] Tauri项目初始化和环境配置
- [ ] 基础UI框架搭建（React + TypeScript + Tailwind）
- [ ] 状态管理系统设置（Zustand）
- [ ] 基础路由和页面结构

**Week 2:**
- [ ] 数据库设计和初始化（SQLite schema）
- [ ] 配置管理系统（本地配置文件）
- [ ] 基础API接口设计
- [ ] 错误处理和日志系统

### 第二阶段：核心功能开发（3周）
**Week 3:**
- [ ] AI对话功能实现（基础聊天界面）
- [ ] DeepSeek API集成
- [ ] 对话历史存储和显示
- [ ] 基础消息组件开发

**Week 4:**
- [ ] 全局快捷键系统开发
- [ ] 浮动窗口实现
- [ ] 快捷操作界面设计
- [ ] 基础工具系统框架

**Week 5:**
- [ ] safe_shell工具实现
- [ ] 工具调用安全机制
- [ ] 记忆管理基础功能（生成、存储）
- [ ] 向量嵌入集成

### 第三阶段：高级功能（2周）
**Week 6:**
- [ ] 记忆智能整理算法
- [ ] 记忆检索和相关性计算
- [ ] 高德地图工具集成
- [ ] 更多系统工具开发

**Week 7:**
- [ ] 设置界面完善
- [ ] 主题系统实现
- [ ] 性能优化（缓存、懒加载）
- [ ] 用户体验改进

### 第四阶段：测试和打包（1周）
**Week 8:**
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] AppImage打包配置
- [ ] 多环境部署测试
- [ ] 用户文档编写
- [ ] 性能基准测试

## 技术实现细节

### 1. AI模型管理架构
```rust
pub struct ModelManager {
    active_model: ModelType,
    models: HashMap<ModelType, Box<dyn AIModel>>,
    fallback_chain: Vec<ModelType>,
}

pub trait AIModel {
    async fn chat(&self, messages: &[Message]) -> Result<String, AIError>;
    async fn chat_with_tools(&self, messages: &[Message], tools: &[Tool]) -> Result<ChatResponse, AIError>;
    fn get_limits(&self) -> ModelLimits;
    fn get_cost(&self) -> CostInfo;
}
```

### 2. 工具调用执行流程
```rust
pub struct ToolExecutor {
    security_manager: SecurityManager,
    timeout_manager: TimeoutManager,
    result_cache: LruCache<String, ToolResult>,
}

impl ToolExecutor {
    async fn execute_tool(&self, tool_call: ToolCall) -> Result<ToolResult, ToolError> {
        // 1. 安全验证
        self.security_manager.validate(&tool_call)?;

        // 2. 检查缓存
        if let Some(cached) = self.result_cache.get(&tool_call.cache_key()) {
            return Ok(cached.clone());
        }

        // 3. 执行工具
        let result = timeout(
            self.timeout_manager.get_timeout(&tool_call.tool_name),
            tool_call.execute()
        ).await??;

        // 4. 缓存结果
        self.result_cache.put(tool_call.cache_key(), result.clone());

        Ok(result)
    }
}
```

### 3. 记忆向量化处理
```rust
pub struct EmbeddingService {
    model: SentenceTransformer,
    cache: LruCache<String, Vec<f32>>,
}

impl EmbeddingService {
    async fn embed_text(&self, text: &str) -> Result<Vec<f32>, EmbeddingError> {
        if let Some(cached) = self.cache.get(text) {
            return Ok(cached.clone());
        }

        let embedding = self.model.encode(&[text]).await?[0].clone();
        self.cache.put(text.to_string(), embedding.clone());

        Ok(embedding)
    }

    fn cosine_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

        dot_product / (norm_a * norm_b)
    }
}
```

## 技术风险和解决方案

### 风险1：全局快捷键冲突
**解决方案**:
- 提供快捷键自定义功能
- 实现冲突检测机制
- 提供备用快捷键方案
- 支持多组合键设置

### 风险2：AI API限制和成本
**解决方案**:
- 实现多模型切换和负载均衡
- 支持本地Ollama模型作为备选
- 添加使用量监控和预算控制
- 实现智能缓存减少API调用

### 风险3：记忆系统性能
**解决方案**:
- 使用高效的向量相似度计算
- 实现记忆分页和懒加载
- 定期清理过期数据
- 使用索引优化数据库查询

### 风险4：AppImage兼容性
**解决方案**:
- 在多个Ubuntu版本测试（20.04, 22.04, 24.04）
- 静态链接关键依赖
- 提供详细的系统要求文档
- 实现运行时依赖检查

### 风险5：内存和性能问题
**解决方案**:
- 实现内存使用监控和限制
- 使用流式处理大量数据
- 优化向量计算算法
- 实现智能垃圾回收策略
### 3. 用户体验和安全模块

#### 3.1 错误处理和用户反馈
**错误分类处理**:
- 网络错误：自动重试机制，降级到本地模型
- API限制错误：显示友好提示，建议切换模型
- 工具执行错误：详细错误信息，安全建议
- 权限错误：引导用户授权或调整设置

**用户反馈机制**:
- 操作成功/失败的视觉反馈
- 进度指示器（长时间操作）
- 音效反馈（可选）
- 震动反馈（支持的设备）

#### 3.2 安全和权限管理
**工具执行安全**:
```rust
pub struct SecurityManager {
    whitelist_commands: HashSet<String>,
    blacklist_patterns: Vec<Regex>,
    max_execution_time: Duration,
    sandbox_mode: bool,
}

impl SecurityManager {
    fn validate_command(&self, command: &str) -> SecurityResult {
        // 检查黑名单
        // 验证参数安全性
        // 检查文件路径权限
        // 评估风险级别
    }
}
```

**权限分级**:
- 安全级别1：只读操作（文件查看、系统信息）
- 安全级别2：有限写入（创建文件、修改配置）
- 安全级别3：系统操作（安装软件、修改系统设置）

#### 3.3 无障碍访问支持
- 键盘完全导航支持
- 屏幕阅读器兼容
- 高对比度主题
- 字体大小调节
- 语音输入支持（未来版本）

### 4. 性能优化和监控

#### 4.1 性能优化策略
**内存管理**:
- 对话历史分页加载
- 记忆向量缓存策略
- 定期垃圾回收
- 内存使用监控

**响应速度优化**:
- AI请求并发处理
- 本地缓存常用响应
- 预加载常用工具
- 懒加载非关键组件

**数据库优化**:
- 索引优化策略
- 查询性能监控
- 定期数据清理
- 备份和恢复机制

#### 4.2 监控和日志
**性能监控**:
- API响应时间统计
- 工具执行时间追踪
- 内存使用趋势
- 错误率统计

**日志系统**:
- 分级日志记录（Error, Warn, Info, Debug）
- 日志轮转和压缩
- 敏感信息过滤
- 远程日志上报（可选）

## 后续扩展计划

### 短期扩展（3个月内）
- 更多工具集成（文件操作、网络查询等）
- 语音交互支持
- 多语言支持
- 插件开发SDK

### 长期扩展（6个月内）
- 插件系统和应用商店
- 云端记忆同步
- 团队协作功能
- 移动端适配