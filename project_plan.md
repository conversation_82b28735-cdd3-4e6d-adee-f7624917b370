# Sparkle-AI 项目规划文档

## 项目概述

### 项目名称
Sparkle-AI - 智能系统助手

### 项目目标
构建一个基于AI的系统助手，通过AppImage格式部署在Ubuntu 24.04系统上，为用户提供便捷的AI交互和系统操作能力。

### 核心价值
- 快捷AI交互：通过全局快捷键快速调用AI能力
- 智能记忆管理：自动记录和整理用户交互历史
- 系统深度集成：通过工具调用实现与系统的无缝交互

## 技术架构方案

### 技术栈选择

#### 前端框架
- **主框架**: Tauri (Rust + Web前端)
  - 优势：轻量级、安全性高、跨平台、易于打包AppImage
  - 前端技术：React + TypeScript + Tailwind CSS
  - 状态管理：Zustand

#### 后端技术
- **核心语言**: Rust
  - 高性能、内存安全、并发处理能力强
- **数据库**: SQLite
  - 轻量级、无需额外部署、适合桌面应用
- **HTTP客户端**: reqwest
- **异步运行时**: tokio

#### AI集成
- **模型接入**:
  - DeepSeek API
  - 通义千问 API  
  - Ollama (本地模型)
- **工具调用框架**: 自定义工具系统

#### 系统集成
- **全局快捷键**: global-hotkey crate
- **系统托盘**: tray-icon crate
- **Shell执行**: std::process::Command

### 项目结构

```
sparkle-ai/
├── src-tauri/           # Rust后端代码
│   ├── src/
│   │   ├── main.rs
│   │   ├── ai/          # AI模块
│   │   ├── tools/       # 工具调用模块
│   │   ├── memory/      # 记忆管理模块
│   │   ├── config/      # 配置管理
│   │   └── database/    # 数据库操作
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                 # 前端代码
│   ├── components/      # React组件
│   ├── pages/          # 页面组件
│   ├── hooks/          # 自定义hooks
│   ├── stores/         # 状态管理
│   └── utils/          # 工具函数
├── public/
├── package.json
└── README.md
```

## 功能需求详细设计

### 1. 快捷操作模块

#### 功能描述
- 全局快捷键唤起（默认Ctrl+Q，可自定义）
- 浮动输入框界面（类似Spotlight/Alfred）
- 快速AI查询和响应
- 智能命令识别和执行
- 历史命令快速访问

#### 技术实现
- 使用global-hotkey监听系统快捷键
- 创建无边框浮动窗口（Always on top）
- 实现快速AI调用接口
- 命令缓存和预测机制
- 键盘导航支持

#### 用户交互流程
1. 用户按下Ctrl+Q
2. 弹出输入框（屏幕中央，半透明背景）
3. 用户输入问题或命令
4. 实时显示匹配的历史命令/建议
5. AI处理并返回结果
6. 显示结果或执行相应操作
7. 按ESC或点击外部区域关闭

#### 界面设计细节
- 输入框：圆角设计，阴影效果
- 结果显示：可滚动区域，支持富文本
- 快捷操作按钮：复制、执行、保存等
- 状态指示器：加载动画、错误提示
- 键盘快捷键：Tab补全、上下箭头选择历史

### 2. 主界面模块

#### 2.1 AI对话功能

**核心特性**:
- 实时对话界面
- 历史记录查看
- 工具调用支持
- 多模型切换

**工具系统设计**:
```rust
pub trait Tool {
    fn name(&self) -> &str;
    fn description(&self) -> &str;
    fn execute(&self, params: serde_json::Value) -> Result<String, ToolError>;
}
```

**已规划工具**:
- `safe_shell`: 安全Shell命令执行
  - 白名单命令机制
  - 参数验证和转义
  - 执行超时控制
  - 输出长度限制
- `gaode_map`: 高德地图API集成（天气、地点查询）
  - 实时天气查询
  - 地点搜索和导航
  - 周边服务查询
- `file_manager`: 文件系统操作
  - 安全的文件读写
  - 目录浏览和搜索
  - 文件类型识别
  - 权限检查机制
- `system_info`: 系统信息查询
  - CPU、内存、磁盘使用率
  - 进程管理
  - 网络状态监控
  - 系统日志查看
- `web_search`: 网络搜索工具
  - 搜索引擎API集成
  - 结果摘要和过滤
  - 网页内容提取
- `calendar_manager`: 日程管理
  - 事件创建和提醒
  - 日历同步
  - 时间冲突检测
- `note_taking`: 笔记管理
  - 快速记录
  - 标签分类
  - 全文搜索
  - Markdown支持

#### 2.2 记忆管理系统

**记忆分类**:
- 重要记忆：用户明确标记或AI判断的关键信息
- 一般记忆：日常对话中的上下文信息

**记忆生命周期**:
1. **生成阶段**: 每次对话后AI自动生成记忆
   - 使用专门的记忆提取prompt
   - 自动识别关键信息和上下文
   - 生成结构化记忆条目
2. **检索阶段**: 对话前自动搜索相关记忆
   - 基于语义相似度的向量搜索
   - 时间衰减权重计算
   - 上下文相关性评分
   - 最多检索5条最相关记忆
3. **整理阶段**: 每20条记忆触发AI整理
   - 相似记忆合并（相似度>0.85）
   - 过期记忆清理（30天未访问且重要性<2）
   - 重要性重新评估
   - 记忆关系图构建

**向量嵌入实现**:
- 使用sentence-transformers模型
- 本地部署轻量级嵌入模型（如all-MiniLM-L6-v2）
- 384维向量存储
- 余弦相似度计算

**记忆检索算法**:
```rust
pub struct MemoryRetrieval {
    similarity_threshold: f32,  // 0.7
    time_decay_factor: f32,     // 0.1
    max_results: usize,         // 5
}

impl MemoryRetrieval {
    fn calculate_score(&self, memory: &Memory, query_embedding: &[f32]) -> f32 {
        let similarity = cosine_similarity(&memory.embedding, query_embedding);
        let time_factor = self.calculate_time_decay(memory.last_accessed);
        let importance_factor = memory.importance_level as f32 / 2.0;

        similarity * 0.6 + time_factor * 0.2 + importance_factor * 0.2
    }
}
```

**数据库设计**:
```sql
-- 记忆表
CREATE TABLE memories (
    id INTEGER PRIMARY KEY,
    content TEXT NOT NULL,
    summary TEXT,               -- AI生成的摘要
    importance_level INTEGER,   -- 1:一般 2:重要 3:关键
    created_at TIMESTAMP,
    last_accessed TIMESTAMP,
    access_count INTEGER DEFAULT 0,
    embedding BLOB,            -- 向量嵌入用于相似性搜索
    tags TEXT,                 -- JSON格式标签
    source_type TEXT,          -- 来源类型：chat, tool, user_input
    related_memories TEXT      -- JSON格式的相关记忆ID列表
);

-- 对话历史表
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY,
    session_id TEXT NOT NULL,
    role TEXT NOT NULL,        -- user, assistant, system
    content TEXT NOT NULL,
    tool_calls TEXT,           -- JSON格式的工具调用记录
    timestamp TIMESTAMP,
    model_used TEXT
);

-- 工具使用记录表
CREATE TABLE tool_usage (
    id INTEGER PRIMARY KEY,
    tool_name TEXT NOT NULL,
    parameters TEXT,           -- JSON格式参数
    result TEXT,
    success BOOLEAN,
    execution_time INTEGER,    -- 毫秒
    timestamp TIMESTAMP,
    conversation_id INTEGER,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id)
);

-- 用户配置表
CREATE TABLE user_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP
);
```

#### 2.3 设置模块

**模型配置**:
- DeepSeek API配置
  - API密钥管理
  - 请求频率限制
  - 模型版本选择
- 通义千问API配置
  - 阿里云账户集成
  - 区域选择
  - 计费监控
- Ollama本地模型配置
  - 模型下载和管理
  - GPU加速设置
  - 内存使用限制

**AI参数设置**:
- 温度参数 (0.1-2.0)
- 最大token数 (100-4000)
- 对话窗口长度（保留多少轮对话，默认10轮）
- 系统提示词自定义
- 工具调用权限控制

**界面配置**:
- 主题设置（亮色/暗色/自动）
- 字体大小 (12-24px)
- 快捷键自定义
- 窗口透明度
- 动画效果开关
- 语言设置

**安全设置**:
- 工具执行权限管理
- 敏感命令黑名单
- 数据加密选项
- 自动备份设置

**性能设置**:
- 记忆检索数量限制
- 向量计算并发数
- 缓存大小设置
- 日志级别配置

## 开发计划

### 第一阶段：基础框架搭建（2周）
- [ ] Tauri项目初始化
- [ ] 基础UI框架搭建
- [ ] 数据库设计和初始化
- [ ] 配置管理系统

### 第二阶段：核心功能开发（3周）
- [ ] AI对话功能实现
- [ ] 快捷键系统开发
- [ ] 基础工具系统（safe_shell）
- [ ] 记忆管理基础功能

### 第三阶段：高级功能（2周）
- [ ] 记忆智能整理
- [ ] 高德地图工具集成
- [ ] 设置界面完善
- [ ] 性能优化

### 第四阶段：测试和打包（1周）
- [ ] 功能测试
- [ ] AppImage打包配置
- [ ] 部署测试
- [ ] 文档完善

## 技术风险和解决方案

### 风险1：全局快捷键冲突
**解决方案**: 提供快捷键自定义功能，检测冲突并提示用户

### 风险2：AI API限制和成本
**解决方案**: 
- 实现多模型切换
- 支持本地Ollama模型
- 添加使用量监控

### 风险3：记忆系统性能
**解决方案**:
- 使用向量数据库进行相似性搜索
- 实现记忆分页和懒加载
- 定期清理过期数据

### 风险4：AppImage兼容性
**解决方案**:
- 在多个Ubuntu版本测试
- 静态链接关键依赖
- 提供详细的系统要求文档
## 后续扩展计划

### 短期扩展（3个月内）
- 更多工具集成（文件操作、网络查询等）
- 语音交互支持
- 多语言支持

### 长期扩展（6个月内）
- 插件系统
- 云端记忆同步
- 团队协作功能